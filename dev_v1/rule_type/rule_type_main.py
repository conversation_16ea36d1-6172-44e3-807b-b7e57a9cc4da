# -*- coding: utf-8 -*-
"""
规则类型分类器主程序
功能：
1. 读取医疗记录JSON文件
2. 对每个规则进行类型分类（内涵/规则）
3. 生成带有类型标注的新JSON文件
"""

import json
import os
import sys
import logging
import argparse
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

# 添加当前目录和父目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.extend([current_dir, parent_dir])

from rule_type_classifier import classify_rule_type, get_available_model_configs, setup_logger

# 设置日志
logger = setup_logger(__name__)


class RuleTypeProcessor:
    """规则类型处理器"""

    def __init__(self, input_dir: str = "../Medical_Record_List/Medical_Record_List_Json",
                 output_dir: str = "rule_type_json",
                 model_config: Optional[Dict[str, Any]] = None):
        """
        初始化处理器
        
        Args:
            input_dir (str): 输入目录
            output_dir (str): 输出目录
            model_config (Optional[Dict[str, Any]]): 模型配置
        """
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.model_config = model_config
        
        # 确保输出目录存在
        self._ensure_output_dir()
        
        logger.info(f"初始化规则类型处理器")
        logger.info(f"输入目录: {os.path.abspath(self.input_dir)}")
        logger.info(f"输出目录: {os.path.abspath(self.output_dir)}")
        
        if self.model_config:
            model_name = self.model_config.get('model', 'Unknown')
            logger.info(f"使用模型: {model_name}")
    
    def _ensure_output_dir(self):
        """确保输出目录存在"""
        try:
            os.makedirs(self.output_dir, exist_ok=True)
            logger.debug(f"输出目录已准备: {self.output_dir}")
        except Exception as e:
            logger.error(f"创建输出目录失败: {e}")
            raise
    
    def _normalize_filename(self, filename: str) -> List[str]:
        """
        生成文件名的不同变体用于查找

        Args:
            filename (str): 原始文件名

        Returns:
            List[str]: 文件名变体列表
        """
        # 移除.json扩展名（如果存在）进行处理
        base_name = filename[:-5] if filename.endswith('.json') else filename

        # 生成不同的文件名变体
        filename_variants = []

        # 1. 原始文件名
        filename_variants.append(base_name)

        # 2. 将空格替换为下划线（主要功能：支持 "Initial Progress Note" -> "Initial_Progress_Note"）
        if ' ' in base_name:
            underscore_version = base_name.replace(' ', '_')
            filename_variants.append(underscore_version)

        # 3. 将下划线替换为空格（反向兼容）
        if '_' in base_name:
            space_version = base_name.replace('_', ' ')
            filename_variants.append(space_version)

        # 4. 处理连字符的情况
        if '-' in base_name:
            # 连字符替换为下划线
            dash_to_underscore = base_name.replace('-', '_')
            filename_variants.append(dash_to_underscore)
            # 连字符替换为空格
            dash_to_space = base_name.replace('-', ' ')
            filename_variants.append(dash_to_space)

            # 处理复合情况：连字符替换为下划线，然后空格也替换为下划线
            if ' ' in base_name:
                dash_and_space_to_underscore = base_name.replace('-', '_').replace(' ', '_')
                filename_variants.append(dash_and_space_to_underscore)

        # 去重并保持顺序
        unique_variants = []
        for variant in filename_variants:
            if variant not in unique_variants:
                unique_variants.append(variant)

        # 为每个变体添加.json扩展名
        return [f"{variant}.json" for variant in unique_variants]

    def _find_input_file(self, filename: str) -> Optional[str]:
        """
        查找输入文件，支持智能文件名匹配

        功能特性：
        - 支持带或不带.json扩展名的文件名
        - 支持空格与下划线的自动转换（如 "Initial Progress Note" -> "Initial_Progress_Note.json"）
        - 支持连字符的处理
        - 智能路径查找

        Args:
            filename (str): 文件名（可带或不带.json扩展名，支持空格）

        Returns:
            Optional[str]: 找到的文件路径，如果未找到则返回None
        """
        logger.debug(f"开始查找输入文件: {filename}")

        # 生成文件名变体
        filename_variants = self._normalize_filename(filename)
        logger.debug(f"生成的文件名变体: {filename_variants}")

        # 定义搜索路径模板
        search_path_templates = [
            # 直接使用配置的输入目录
            self.input_dir,

            # 从当前目录开始的相对路径
            os.path.join(".", self.input_dir),

            # 从父目录开始的路径
            os.path.join("..", self.input_dir),

            # 尝试不同层级的Medical_Record_List_Json目录
            "Medical_Record_List_Json",
            "../Medical_Record_List_Json",
            "../Medical_Record_List/Medical_Record_List_Json",
            "../../Medical_Record_List/Medical_Record_List_Json",

            # 尝试dev_v1目录下的路径
            "../Medical_Record_List/Medical_Record_List_Json",
            "../../dev_v1/Medical_Record_List/Medical_Record_List_Json",
        ]

        # 尝试每个文件名变体和每个路径组合
        attempted_paths = []
        for filename_variant in filename_variants:
            # 首先尝试直接使用文件名（如果是完整路径）
            if os.path.exists(filename_variant):
                abs_path = os.path.abspath(filename_variant)
                logger.info(f"找到输入文件: {abs_path} (使用文件名变体: {filename_variant})")
                return filename_variant
            attempted_paths.append(filename_variant)

            # 然后尝试各个搜索路径
            for path_template in search_path_templates:
                full_path = os.path.join(path_template, filename_variant)
                attempted_paths.append(full_path)

                if os.path.exists(full_path):
                    abs_path = os.path.abspath(full_path)
                    logger.info(f"找到输入文件: {abs_path} (使用文件名变体: {filename_variant})")
                    return full_path

        # 如果所有尝试都失败了
        logger.error(f"未找到输入文件: {filename}")
        logger.info(f"尝试的文件名变体: {filename_variants}")
        logger.debug("尝试过的路径:")
        for path in attempted_paths:
            abs_path = os.path.abspath(path)
            exists_status = "✅" if os.path.exists(path) else "❌"
            logger.debug(f"  {exists_status} {abs_path}")

        return None
    
    def process_file(self, input_filename: str) -> Optional[str]:
        """
        处理单个文件
        
        Args:
            input_filename (str): 输入文件名
            
        Returns:
            Optional[str]: 输出文件路径，如果处理失败则返回None
        """
        logger.info(f"开始处理文件: {input_filename}")
        
        try:
            # 查找输入文件
            input_file_path = self._find_input_file(input_filename)
            if not input_file_path:
                return None
            
            # 读取输入文件
            with open(input_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            records = data.get('records', [])
            logger.info(f"成功读取文件，包含 {len(records)} 条规则")
            
            if not records:
                logger.warning("文件中没有找到规则记录")
                return None
            
            # 处理每个规则
            processed_records = []
            classification_stats = {"内涵": 0, "规则": 0}
            
            for i, record in enumerate(records, 1):
                logger.debug(f"处理第 {i}/{len(records)} 条规则")
                
                # 调用分类函数
                rule_type = classify_rule_type(record, self.model_config)
                
                # 添加type字段到规则中
                processed_record = record.copy()
                processed_record['type'] = rule_type
                processed_records.append(processed_record)
                
                # 统计分类结果
                classification_stats[rule_type] += 1
            
            # 更新数据结构
            processed_data = data.copy()
            processed_data['records'] = processed_records
            
            # 更新元数据
            if 'metadata' in processed_data:
                processed_data['metadata']['processing_time'] = datetime.now().isoformat()
                processed_data['metadata']['type_classification_added'] = True
                processed_data['metadata']['classification_stats'] = classification_stats
            
            # 生成输出文件名 - 使用实际找到的文件名（下划线格式）而不是用户输入的文件名
            actual_filename = os.path.basename(input_file_path)  # 获取实际找到的文件名
            base_filename = os.path.splitext(actual_filename)[0]  # 移除.json扩展名
            output_filename = f"{base_filename}_type.json"
            output_file_path = os.path.join(self.output_dir, output_filename)

            logger.debug(f"输出文件名生成: 用户输入='{input_filename}' -> 实际文件='{actual_filename}' -> 输出文件='{output_filename}'")
            
            # 保存处理后的数据
            with open(output_file_path, 'w', encoding='utf-8') as f:
                json.dump(processed_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"处理完成，结果已保存到: {os.path.abspath(output_file_path)}")
            logger.info(f"分类统计: {classification_stats}")
            
            return output_file_path
            
        except FileNotFoundError as e:
            logger.error(f"文件未找到: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
            return None
        except Exception as e:
            logger.error(f"处理过程中发生错误: {e}")
            return None


def get_model_config_by_name(config_name: str) -> Optional[Dict[str, Any]]:
    """
    根据配置名称获取模型配置
    
    Args:
        config_name (str): 配置名称
        
    Returns:
        Optional[Dict[str, Any]]: 模型配置，如果未找到则返回None
    """
    configs = get_available_model_configs()
    
    if config_name in configs:
        logger.info(f"使用模型配置: {config_name}")
        return configs[config_name]
    else:
        logger.error(f"未找到模型配置: {config_name}")
        logger.info(f"可用的配置: {list(configs.keys())}")
        return None


def setup_argument_parser() -> argparse.ArgumentParser:
    """
    设置命令行参数解析器
    
    Returns:
        argparse.ArgumentParser: 参数解析器
    """
    parser = argparse.ArgumentParser(
        description="规则类型分类器 - 对医疗质控规则进行内涵/规则类型分类",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 使用下划线格式的文件名
  python rule_type_main.py --input-file "Admission_Record"
  python rule_type_main.py --input-file "Initial_Progress_Note"

  # 使用空格格式的文件名（自动转换为下划线格式）
  python rule_type_main.py --input-file "Initial Progress Note"
  python rule_type_main.py --input-file "Discharge Summary"

  # 指定模型配置和输出目录
  python rule_type_main.py --input-file "Medical Order" --model-config "glm_code_config"
  python rule_type_main.py --input-file "Postoperative Initial Progress Note" --output-dir "custom_output"
        """
    )
    
    parser.add_argument(
        '--input-file', '-i',
        type=str,
        help='输入文件名（可不包含.json扩展名，支持空格自动转换为下划线，如 "Initial Progress Note" 会查找 "Initial_Progress_Note.json"）'
    )
    
    parser.add_argument(
        '--input-dir',
        type=str,
        default='../Medical_Record_List/Medical_Record_List_Json',
        help='输入目录 (默认: ../Medical_Record_List/Medical_Record_List_Json)'
    )
    
    parser.add_argument(
        '--output-dir', '-o',
        type=str,
        default='rule_type_json',
        help='输出目录 (默认: rule_type_json)'
    )
    
    parser.add_argument(
        '--model-config', '-m',
        type=str,
        default='qwen_32B_config',
        choices=['glm_code_config', 'deepseek_r1_config', 'deepseek_v3_config', 
                'kimi_k2_config', 'qwen_30B_config', 'qwen_32B_config'],
        help='模型配置 (默认: qwen_32B_config)'
    )
    
    parser.add_argument(
        '--log-level',
        type=str,
        default='INFO',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='日志级别 (默认: INFO)'
    )
    
    return parser


def interactive_mode() -> Tuple[str, str, str, str]:
    """
    交互模式获取用户输入
    
    Returns:
        Tuple[str, str, str, str]: (输入文件名, 输入目录, 输出目录, 模型配置名)
    """
    print("=" * 60)
    print("规则类型分类器")
    print("=" * 60)
    
    # 获取输入文件名
    print("💡 提示: 支持使用空格的文件名，如 'Initial Progress Note' 会自动查找 'Initial_Progress_Note.json'")
    input_file = input("请输入要处理的文件名（可不包含.json扩展名，支持空格）: ").strip()
    if not input_file:
        print("❌ 文件名不能为空")
        return interactive_mode()
    
    # 获取输入目录
    input_dir = input("请输入输入目录 (默认: ../Medical_Record_List/Medical_Record_List_Json): ").strip()
    if not input_dir:
        input_dir = "../Medical_Record_List/Medical_Record_List_Json"
    
    # 获取输出目录
    output_dir = input("请输入输出目录 (默认: rule_type_json): ").strip()
    if not output_dir:
        output_dir = "rule_type_json"
    
    # 获取模型配置
    print("\n可用的模型配置:")
    configs = get_available_model_configs()
    config_list = list(configs.keys())
    
    for i, (name, config) in enumerate(configs.items(), 1):
        model_name = config.get('model', 'Unknown')
        default_marker = " [默认]" if name == "qwen_32B_config" else ""
        print(f"  {i}. {name}: {model_name}{default_marker}")
    
    while True:
        choice = input(f"请选择模型配置 (1-{len(config_list)}, 默认: 6): ").strip()
        if not choice:
            choice = "6"  # 默认选择 qwen_32B_config
        
        try:
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(config_list):
                model_config_name = config_list[choice_idx]
                break
            else:
                print(f"❌ 请输入 1-{len(config_list)} 之间的数字")
        except ValueError:
            print("❌ 请输入有效的数字")
    
    return input_file, input_dir, output_dir, model_config_name


def main():
    """主函数"""
    try:
        # 设置命令行参数解析器
        parser = setup_argument_parser()
        args = parser.parse_args()

        # 设置日志级别
        log_level = getattr(logging, args.log_level.upper())
        logger.setLevel(log_level)

        # 获取参数
        if args.input_file:
            # 命令行模式
            input_file = args.input_file
            input_dir = args.input_dir
            output_dir = args.output_dir
            model_config_name = args.model_config

            logger.info("使用命令行参数模式")
        else:
            # 交互模式
            logger.info("使用交互模式")
            input_file, input_dir, output_dir, model_config_name = interactive_mode()

        # 获取模型配置
        model_config = get_model_config_by_name(model_config_name)
        if not model_config:
            print(f"❌ 无法获取模型配置: {model_config_name}")
            return

        # 创建处理器
        processor = RuleTypeProcessor(
            input_dir=input_dir,
            output_dir=output_dir,
            model_config=model_config
        )

        # 处理文件
        print(f"\n开始处理文件: {input_file}")
        print(f"输入目录: {input_dir}")
        print(f"输出目录: {output_dir}")
        print(f"使用模型: {model_config.get('model', 'Unknown')}")
        print("-" * 60)

        output_file = processor.process_file(input_file)

        if output_file:
            print("\n" + "=" * 60)
            print("✅ 处理完成！")
            print("=" * 60)
            print(f"输入文件: {input_file}")
            print(f"输出文件: {os.path.abspath(output_file)}")

            # 显示处理统计信息
            try:
                with open(output_file, 'r', encoding='utf-8') as f:
                    result_data = json.load(f)

                metadata = result_data.get('metadata', {})
                stats = metadata.get('classification_stats', {})
                total_records = metadata.get('total_records', 0)

                if stats:
                    print(f"\n📊 分类统计:")
                    print(f"  总规则数: {total_records}")
                    print(f"  内涵质控: {stats.get('内涵', 0)} 条")
                    print(f"  规则质控: {stats.get('规则', 0)} 条")

                    if total_records > 0:
                        print(f"\n📈 分类比例:")
                        print(f"  内涵质控: {stats.get('内涵', 0)/total_records*100:.1f}%")
                        print(f"  规则质控: {stats.get('规则', 0)/total_records*100:.1f}%")

            except Exception as e:
                logger.warning(f"无法读取统计信息: {e}")

            print(f"\n💡 提示:")
            print(f"  - 输出文件已保存到: {os.path.dirname(os.path.abspath(output_file))}")
            print(f"  - 日志文件: rule_type_classifier.log")
            print(f"  - 每条规则已添加 'type' 字段标识分类结果")

        else:
            print("\n❌ 处理失败，请查看日志了解详细信息")
            print("💡 常见问题:")
            print("  1. 检查输入文件是否存在")
            print("  2. 确认JSON文件格式正确")
            print("  3. 验证网络连接和模型配置")
            print("  4. 查看日志文件获取详细错误信息")

    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"主程序执行出错: {e}")
        print(f"\n❌ 执行出错: {e}")
        print("💡 请查看日志文件获取详细错误信息")


if __name__ == "__main__":
    main()
