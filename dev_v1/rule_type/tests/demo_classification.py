#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗记录质量控制分类系统演示
展示新的分类逻辑如何工作
"""

import sys
import os

# 添加父目录到路径以导入核心模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from medical_document_standards import (
    classify_rule_by_standards,
    normalize_document_type,
    get_required_sections
)

def demo_classification():
    """演示分类功能"""
    print("🏥 医疗记录质量控制分类系统演示")
    print("=" * 60)
    
    # 演示案例
    demo_cases = [
        {
            "name": "规则质控案例1：章节缺失",
            "rule": {
                "rule_content": "首次病程缺病例特点",
                "rule_type_chinese": "段落完整性",
                "document_type_chinese": "首次病程记录",
                "belonging_project_chinese": "病程记录"
            }
        },
        {
            "name": "规则质控案例2：时效性问题",
            "rule": {
                "rule_content": "入院记录未在24小时内完成",
                "rule_type_chinese": "时效性",
                "document_type_chinese": "入院记录",
                "belonging_project_chinese": "入院记录"
            }
        },
        {
            "name": "内涵质控案例1：内容质量问题",
            "rule": {
                "rule_content": "主诉描述有缺陷",
                "rule_type_chinese": "内容完整性",
                "document_type_chinese": "入院记录",
                "belonging_project_chinese": "入院记录"
            }
        },
        {
            "name": "内涵质控案例2：内容不充分",
            "rule": {
                "rule_content": "现病史缺发病情况",
                "rule_type_chinese": "内容完整性",
                "document_type_chinese": "入院记录",
                "belonging_project_chinese": "入院记录"
            }
        },
        {
            "name": "内涵质控案例3：数据一致性",
            "rule": {
                "rule_content": "现病史主要症状或体征、症状性质、持续时间与主诉不符",
                "rule_type_chinese": "数据一致性",
                "document_type_chinese": "入院记录",
                "belonging_project_chinese": "入院记录"
            }
        }
    ]
    
    for i, case in enumerate(demo_cases, 1):
        print(f"\n{i}. {case['name']}")
        print("-" * 40)
        
        rule = case['rule']
        
        # 显示规则信息
        print(f"📋 规则内容: {rule['rule_content']}")
        print(f"📋 规则类型: {rule['rule_type_chinese']}")
        print(f"📋 文档类型: {rule['document_type_chinese']}")
        
        # 执行分类
        classification = classify_rule_by_standards(rule)
        
        # 显示分类结果
        icon = "🔧" if classification == "规则" else "📋"
        print(f"{icon} 分类结果: {classification}质控")
        
        # 显示分类依据
        if classification == "规则":
            print("💡 分类依据: 结构完整性问题（整个章节缺失或时效性问题）")
        else:
            print("💡 分类依据: 内容充分性问题（章节存在但内容不足或质量不达标）")

def demo_document_standards():
    """演示文档标准功能"""
    print(f"\n" + "=" * 60)
    print("📚 医疗文档标准演示")
    print("=" * 60)
    
    # 演示文档类型标准化
    print("\n1. 文档类型标准化")
    print("-" * 30)
    
    test_types = ["首次病程", "首次病程记录", "入院病历", "出院小结"]
    for doc_type in test_types:
        normalized = normalize_document_type(doc_type)
        print(f"  {doc_type} → {normalized}")
    
    # 演示必需章节获取
    print("\n2. 必需章节要求")
    print("-" * 30)
    
    standard_docs = ["首次病程记录", "入院记录", "出院记录"]
    for doc_type in standard_docs:
        sections = get_required_sections(doc_type)
        print(f"\n📋 {doc_type}:")
        for section in sections:
            print(f"  ✓ {section}")

def interactive_demo():
    """交互式演示"""
    print(f"\n" + "=" * 60)
    print("🎯 交互式分类演示")
    print("=" * 60)
    
    print("\n请输入规则信息进行分类测试：")
    
    while True:
        try:
            print("\n" + "-" * 40)
            rule_content = input("规则内容 (输入 'quit' 退出): ").strip()
            
            if rule_content.lower() == 'quit':
                break
                
            if not rule_content:
                print("❌ 规则内容不能为空")
                continue
            
            rule_type = input("规则类型 (如：段落完整性、内容完整性、时效性): ").strip()
            doc_type = input("文档类型 (如：首次病程记录、入院记录): ").strip()
            
            # 构建规则数据
            rule_data = {
                "rule_content": rule_content,
                "rule_type_chinese": rule_type or "未知",
                "document_type_chinese": doc_type or "未知"
            }
            
            # 执行分类
            classification = classify_rule_by_standards(rule_data)
            
            # 显示结果
            icon = "🔧" if classification == "规则" else "📋"
            print(f"\n{icon} 分类结果: {classification}质控")
            
            if classification == "规则":
                print("💡 这是一个结构完整性问题（章节缺失、时效性或格式问题）")
            else:
                print("💡 这是一个内容充分性问题（内容质量、完整性或一致性问题）")
                
        except KeyboardInterrupt:
            print("\n\n👋 演示结束")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

def main():
    """主演示函数"""
    try:
        demo_classification()
        demo_document_standards()
        
        print(f"\n" + "=" * 60)
        print("✅ 演示完成！")
        
        # 询问是否进行交互式演示
        response = input("\n是否进行交互式分类测试？(y/n): ").strip().lower()
        if response in ['y', 'yes', '是']:
            interactive_demo()
        
        print("\n👋 感谢使用医疗记录质量控制分类系统！")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
