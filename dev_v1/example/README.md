# 使用示例模块 (Example Usage)

## 模块概述

本模块提供了医疗病历质控智能代理系统各个组件的使用示例和演示代码，帮助开发者快速理解和使用系统功能。

## 目录结构

```
dev_v1/example/
├── example_usage.py    # Excel转JSON转换器使用示例
└── README.md          # 本说明文档
```

## 示例文件说明

### example_usage.py
**功能**：Excel转JSON转换器的完整使用示例

**主要演示内容**：
1. **基本使用示例**
   - 创建转换器实例
   - 处理Excel文件
   - 获取处理统计信息

2. **高级分析功能**
   - 规则数据分析
   - 文档类型统计
   - 高分规则筛选

3. **错误处理演示**
   - 依赖包检查
   - 文件存在性验证
   - 异常处理机制

## 使用方法

### 运行基本示例
```bash
cd dev_v1/example
python example_usage.py
```

### 示例代码结构
```python
# 1. 基本使用示例
def example_basic_usage():
    converter = ExcelToJsonConverter()
    success = converter.process_excel_file(excel_file)
    
# 2. 分析功能示例  
def example_analyze_rules():
    stats = converter.get_statistics()
    # 分析处理结果
```

## 支持的Excel文件格式

示例支持处理以下类型的Excel文件：
- `首次病程录质控.xlsx` - 首次病程记录质控规则
- `质控评分表v2.0.xlsx` - 综合质控评分标准
- 其他符合标准格式的医疗质控Excel文件

## 输出示例

### 成功处理输出
```
=== 基本使用示例 ===

处理文件: ../doc/首次病程录质控.xlsx
✓ 成功处理 45 条规则
✓ 涉及 3 种文档类型

=== 规则分析示例 ===
总规则数: 45
文档类型: ['首次病程记录', '入院记录', '病程记录']

各类型规则分布:
  首次病程记录: 25 条
  入院记录: 12 条  
  病程记录: 8 条

高分规则（≥6分）: 8 条
  - 缺病例特点: 8分
  - 缺诊断依据: 7分
  - 缺鉴别诊断: 6分
```

## 依赖要求

### Python包依赖
```python
import pandas as pd      # Excel文件处理
import openpyxl         # Excel文件读写
import os, sys          # 系统操作
```

### 项目模块依赖
```python
from excel_to_json_converter import ExcelToJsonConverter
```

## 扩展示例

### 添加新的示例功能
1. 在example_usage.py中添加新的示例函数
2. 遵循现有的命名规范：`example_功能名称()`
3. 添加适当的错误处理和输出格式
4. 在main()函数中调用新示例

### 创建新的示例文件
1. 创建新的Python文件，如`example_api_usage.py`
2. 实现特定功能的使用示例
3. 添加完整的文档字符串和注释
4. 更新本README文档

## 常见问题

### Q: 示例运行失败怎么办？
A: 检查以下几点：
1. 确保安装了所有依赖包：`pip install -r requirements.txt`
2. 验证Excel文件路径是否正确
3. 检查文件权限和访问权限

### Q: 如何修改Excel文件路径？
A: 在example_usage.py中修改excel_files列表：
```python
excel_files = [
    "your/custom/path/file.xlsx",  # 自定义路径
]
```

### Q: 如何添加自定义分析逻辑？
A: 参考example_analyze_rules()函数，添加自定义的数据分析代码。

## 最佳实践

1. **路径管理**：使用相对路径，确保示例在不同环境下可运行
2. **错误处理**：为所有文件操作添加异常处理
3. **输出格式**：保持一致的输出格式，便于理解
4. **代码注释**：为关键逻辑添加详细注释

---

本模块为开发者提供了完整的使用参考，通过实际示例展示系统各组件的正确使用方法，是快速上手项目的最佳起点。
