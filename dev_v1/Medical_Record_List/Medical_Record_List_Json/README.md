# Medical Record List JSON Files

## 文件夹说明

这个文件夹包含了从医疗记录列表生成器生成的所有JSON文件。

## 文件统计

- **总文件数**: 71个JSON文件
- **来源目录**: `dev_v1/Medical_Record_List/`
- **整理时间**: 2025-08-01
- **文件类型**: 医疗文档记录JSON格式

## 文件内容

每个JSON文件包含以下信息：
- 元数据（文档类型、记录数量、生成时间等）
- 中英文翻译对照
- 具体的医疗记录数据

## 文件命名规则

文件名采用英文翻译的医疗文档类型命名，例如：
- `Admission_Record.json` - 入院记录
- `Discharge_Summary.json` - 出院记录
- `Medical_Order.json` - 医嘱
- `Surgical_Record.json` - 手术记录
- 等等...

## 使用说明

这些JSON文件可以用于：
1. 医疗数据分析
2. 质控规则研究
3. 医疗文档标准化
4. 系统集成和数据交换

## 文件结构示例

```json
{
  "metadata": {
    "document_type_chinese": "入院记录",
    "document_type_english": "Admission Record",
    "total_records": 70,
    "generated_time": "2025-08-01T...",
    "version": "1.0"
  },
  "translations": {
    "规则类型": {...},
    "分类": {...},
    "所属项目": {...},
    "文书类型": {...}
  },
  "records": [...]
}
```

## 注意事项

- 所有文件均为UTF-8编码
- JSON格式符合标准规范
- 包含中英文对照翻译
- 数据来源于质控评分表v2.0
