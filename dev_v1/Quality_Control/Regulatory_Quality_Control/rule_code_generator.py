# -*- coding: utf-8 -*-
import os
import sys
import json
import logging
from datetime import datetime

# 添加父目录到路径以导入model_use
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from model_use import llm_use
from config import kimi_k2_config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RuleCodeGenerator:
    """规则质控代码生成器"""
    
    def __init__(self, model_config = None):
        self.model_config = model_config or kimi_k2_config
        logger.info("规则质控代码生成器初始化完成")
    
    def generate_function_code(self, rule):
        """为单个规则生成Python函数代码"""
        system_prompt = """
你是一个专业的医疗质控代码生成助手。请根据提供的质控规则，生成一个Python函数来检查医疗记录是否符合该规则。

要求：
1. 函数名使用rule_id命名
2. 函数接受一个参数：medical_record（医疗记录字典）
3. 返回一个字典，包含：
   - "passed": bool（是否通过检查）
   - "score_deduction": int（扣分，如果通过则为0）
   - "message": str（检查结果说明）
   - "rule_id": str（规则ID）
4. 添加适当的注释和错误处理
5. 只返回函数代码，不要包含其他内容
        """

        user_prompt = f"""
请为以下质控规则生成Python检查函数：

规则ID: {rule['rule_id']}
规则名称: {rule['rule_name']}
文档类型: {rule['document_type']}
规则类型: {rule['rule_type']}
分类: {rule['category']}
扣分: {rule['score']}
描述: {rule.get('description', '')}

请生成对应的Python函数代码。
        """

        try:
            logger.info(f"正在为规则 {rule['rule_id']} 生成代码")
            function_code = llm_use(system_prompt, user_prompt, self.model_config)
            # 清理返回的代码，去除markdown格式
            lines = function_code.strip().split('\n')
            if lines and lines[0].strip() == '```python':
                lines = lines[1:]
            if lines and lines[-1].strip() == '```':
                lines = lines[:-1]
            function_code = '\n'.join(lines)
            return function_code
        except Exception as e:
            logger.error(f"生成规则 {rule['rule_id']} 代码时出错: {e}")
            return None
    
    def generate_file_content(self, rules, document_type):
        """为一组规则生成完整的Python文件内容"""
        logger.info(f"开始为文档类型 {document_type} 生成代码文件")
        
        # 文件头部
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        header = f'''# -*- coding: utf-8 -*-
"""
医疗质控规则检查函数 - {document_type}
自动生成时间: {current_time}
包含 {len(rules)} 个质控规则
"""

import logging
from typing import Dict, Any

# 配置日志
logger = logging.getLogger(__name__)
'''
        
        # 生成所有函数
        functions = []
        for rule in rules:
            function_code = self.generate_function_code(rule)
            if function_code:
                functions.append(function_code)
            else:
                logger.warning(f"跳过规则 {rule['rule_id']}，代码生成失败")
        
        # 生成主检查函数的函数调用列表
        rule_calls = []
        for rule in rules:
            rule_call = f'''    try:
        result = {rule["rule_id"]}(medical_record)
        results[result["rule_id"]] = result
        total_score_deduction += result["score_deduction"]
    except Exception as e:
        logger.error(f"规则 {rule["rule_id"]} 检查时出错: {{e}}")
        results["{rule["rule_id"]}"] = {{
            "passed": False,
            "score_deduction": {rule["score"]},
            "message": f"检查出错: {{e}}",
            "rule_id": "{rule["rule_id"]}"
        }}'''
            rule_calls.append(rule_call)
        
        # 生成主检查函数
        main_function = f'''def check_all_rules(medical_record: Dict[str, Any]) -> Dict[str, Any]:
    """
    检查医疗记录是否符合所有{document_type}质控规则
    
    Args:
        medical_record: 医疗记录字典
    
    Returns:
        检查结果字典，包含每个规则的检查结果
    """
    results = {{}}
    total_score_deduction = 0
    
    logger.info(f"开始检查{document_type}质控规则，共{len(rules)}个规则")
    
    # 调用所有规则检查函数
{chr(10).join(rule_calls)}
    
    # 汇总结果
    passed_count = sum(1 for result in results.values() if result["passed"])
    failed_count = len(results) - passed_count
    
    summary = {{
        "total_rules": len(results),
        "passed_count": passed_count,
        "failed_count": failed_count,
        "total_score_deduction": total_score_deduction,
        "individual_results": results
    }}
    
    logger.info(f"{document_type}质控检查完成，通过: {{passed_count}}, 失败: {{failed_count}}, 总扣分: {{total_score_deduction}}")
    
    return summary
'''
        
        # 组合完整文件内容
        full_content = header + "\n\n" + "\n\n".join(functions) + "\n\n" + main_function
        
        logger.info(f"文档类型 {document_type} 的代码文件生成完成")
        return full_content
    
    def save_generated_code(self, rules, document_type, output_dir):
        """保存生成的代码到文件"""
        try:
            # 生成文件内容
            file_content = self.generate_file_content(rules, document_type)
            
            # 生成文件名
            filename = f"{document_type.replace(' ', '_')}_regulatory_quality_control.py"
            filepath = os.path.join(output_dir, filename)
            
            # 保存文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(file_content)
            
            logger.info(f"代码文件已保存到: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"保存代码文件时出错: {e}")
            return None

if __name__ == '__main__':
    # 测试代码生成器
    generator = RuleCodeGenerator()
    print("规则质控代码生成器测试完成")