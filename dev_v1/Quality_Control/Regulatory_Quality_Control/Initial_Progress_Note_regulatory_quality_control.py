# -*- coding: utf-8 -*-
"""
医疗质控规则检查函数 - Initial Progress Note
自动生成时间: 2025-07-25 16:50:00
包含 6 个质控规则
"""

import logging
from typing import Dict, Any
from datetime import datetime

# 配置日志
logger = logging.getLogger(__name__)


def rule_5e6b4944(medical_record: dict) -> dict:
    """
    检查首次病程记录是否缺少"诊断及诊断依据"段落。

    参数:
        medical_record (dict): 医疗记录字典，应包含：
            - "document_type": str，文档类型
            - "content": str，文档正文内容

    返回:
        dict: 检查结果
            - passed (bool): 是否通过检查
            - score_deduction (int): 扣分值
            - message (str): 检查结果说明
            - rule_id (str): 规则ID
    """
    rule_id = "rule_5e6b4944"
    score_deduction = 6

    try:
        # 校验输入
        if not isinstance(medical_record, dict):
            raise ValueError("medical_record 必须是字典类型")

        document_type = medical_record.get("document_type", "")
        content = medical_record.get("content", "")

        # 仅对"首次病程记录"进行检查
        if document_type.strip() != "Initial Progress Note":
            return {
                "passed": True,
                "score_deduction": 0,
                "message": "非首次病程记录，无需检查。",
                "rule_id": rule_id
            }

        # 定义诊断及诊断依据段落的关键字
        keywords = ["诊断及诊断依据", "诊断依据", "初步诊断", "诊断"]

        # 检查是否包含诊断及诊断依据段落
        has_diagnosis_section = any(keyword in content for keyword in keywords)

        if has_diagnosis_section:
            return {
                "passed": True,
                "score_deduction": 0,
                "message": "首次病程记录包含诊断及诊断依据段落。",
                "rule_id": rule_id
            }
        else:
            return {
                "passed": False,
                "score_deduction": score_deduction,
                "message": "首次病程记录缺少诊断及诊断依据段落。",
                "rule_id": rule_id
            }

    except Exception as e:
        return {
            "passed": False,
            "score_deduction": score_deduction,
            "message": f"检查过程中发生错误：{str(e)}",
            "rule_id": rule_id
        }


def rule_348799f5(medical_record: dict) -> dict:
    """
    检查首次病程记录是否缺少"病例特点"段落。

    参数:
        medical_record (dict): 医疗记录字典，应包含文档类型及段落内容。

    返回:
        dict: 检查结果，包含是否通过、扣分、说明信息及规则ID。
    """
    rule_id = "rule_348799f5"
    required_doc_type = "Initial Progress Note"
    required_paragraph = "病例特点"

    try:
        # 检查文档类型是否为首次病程记录
        doc_type = medical_record.get("document_type", "")
        if doc_type != required_doc_type:
            return {
                "passed": True,
                "score_deduction": 0,
                "message": f"文档类型为{doc_type}，不适用本规则。",
                "rule_id": rule_id
            }

        # 获取段落内容
        paragraphs = medical_record.get("paragraphs", {})
        if not isinstance(paragraphs, dict):
            return {
                "passed": False,
                "score_deduction": 6,
                "message": "段落数据格式异常，无法识别“病例特点”段落。",
                "rule_id": rule_id
            }

        # 检查是否存在"病例特点"段落
        if required_paragraph in paragraphs and paragraphs[required_paragraph].strip():
            return {
                "passed": True,
                "score_deduction": 0,
                "message": "已包含“病例特点“段落。",
                "rule_id": rule_id
            }
        else:
            return {
                "passed": False,
                "score_deduction": 6,
                "message": "首次病程记录缺少“病例特点”段落。",
                "rule_id": rule_id
            }

    except Exception as e:
        return {
            "passed": False,
            "score_deduction": 6,
            "message": f"检查过程中发生异常：{str(e)}",
            "rule_id": rule_id
        }


def rule_d40e19cb(medical_record: dict) -> dict:
    """
    检查首次病程记录是否缺少"诊疗计划"段落。

    参数
    ----
    medical_record : dict
        医疗记录字典，应包含：
        - "document_type": str，文档类型
        - "content": str，文档正文内容

    返回
    ----
    dict
        {
            "passed": bool,        # 是否通过检查
            "score_deduction": int,# 扣分值
            "message": str,        # 检查结果说明
            "rule_id": str         # 规则ID
        }
    """
    rule_id = "rule_d40e19cb"
    score_deduction = 6

    try:
        # 1. 校验文档类型
        if medical_record.get("document_type") != "Initial Progress Note":
            return {
                "passed": True,
                "score_deduction": 0,
                "message": "非首次病程记录，无需检查诊疗计划段落。",
                "rule_id": rule_id
            }

        # 2. 获取正文内容
        content = medical_record.get("content", "")
        if not isinstance(content, str):
            raise TypeError("content 应为字符串类型")

        # 3. 定义诊疗计划关键词
        keywords = ["诊疗计划", "治疗计划", "诊疗方案", "治疗方案"]

        # 4. 检查是否包含任一关键词
        found = any(kw in content for kw in keywords)

        if found:
            return {
                "passed": True,
                "score_deduction": 0,
                "message": "首次病程记录已包含诊疗计划段落。",
                "rule_id": rule_id
            }
        else:
            return {
                "passed": False,
                "score_deduction": score_deduction,
                "message": "首次病程记录缺少诊疗计划段落。",
                "rule_id": rule_id
            }

    except Exception as e:
        return {
            "passed": False,
            "score_deduction": score_deduction,
            "message": f"检查过程中发生异常：{str(e)}",
            "rule_id": rule_id
        }


def rule_0462ecb1(medical_record: dict) -> dict:
    """
    检查首次病程记录是否缺少"鉴别诊断"段落。
    如果诊断中存在"待查"类描述，则不做判断，直接通过。

    参数:
        medical_record (dict): 医疗记录字典，应包含：
            - "document_type": str, 文档类型
            - "diagnosis": list[str], 诊断列表
            - "sections": dict, 段落名称到内容的映射

    返回:
        dict: 检查结果
            - "passed": bool, 是否通过
            - "score_deduction": int, 扣分
            - "message": str, 检查结果说明
            - "rule_id": str, 规则ID
    """
    rule_id = "rule_0462ecb1"

    try:
        # 仅处理首次病程记录
        if medical_record.get("document_type") != "Initial Progress Note":
            return {
                "passed": True,
                "score_deduction": 0,
                "message": "非首次病程记录，无需检查。",
                "rule_id": rule_id
            }

        # 获取诊断列表
        diagnosis_list = medical_record.get("diagnosis", [])
        if not isinstance(diagnosis_list, list):
            diagnosis_list = [diagnosis_list]

        # 检查诊断中是否包含"待查"类描述
        pending_keywords = ["待查", "待排", "待明确", "待鉴别", "待诊断"]
        has_pending = any(
            any(keyword in str(diag) for keyword in pending_keywords)
            for diag in diagnosis_list
        )

        if has_pending:
            return {
                "passed": True,
                "score_deduction": 0,
                "message": "诊断中存在待查类描述，无需检查鉴别诊断段落。",
                "rule_id": rule_id
            }

        # 检查是否存在"鉴别诊断"段落
        sections = medical_record.get("sections", {})
        differential_diagnosis = sections.get("鉴别诊断") or sections.get("鉴别诊断段落")

        if differential_diagnosis and str(differential_diagnosis).strip():
            return {
                "passed": True,
                "score_deduction": 0,
                "message": "已包含鉴别诊断段落。",
                "rule_id": rule_id
            }
        else:
            return {
                "passed": False,
                "score_deduction": 6,
                "message": "首次病程记录缺少鉴别诊断段落。",
                "rule_id": rule_id
            }

    except Exception as e:
        return {
            "passed": False,
            "score_deduction": 6,
            "message": f"检查过程中发生异常：{str(e)}",
            "rule_id": rule_id
        }


def rule_f0551fd5(medical_record: dict) -> dict:
    """
    检查首次病程记录是否缺少医师签名，或实习医务人员书写时是否缺少经治医师/带教老师双签名。

    参数:
        medical_record (dict): 医疗记录字典，应包含：
            - "document_type": str, 文档类型
            - "signatures": list[dict], 签名列表，每个元素包含：
                - "signer_role": str, 签名者角色
                - "signer_type": str, 签名者类型（如"attending", "resident", "intern"等）

    返回:
        dict: 检查结果，包含：
            - "passed": bool, 是否通过检查
            - "score_deduction": int, 扣分（0）
            - "message": str, 检查结果说明
            - "rule_id": str, 规则ID
    """
    rule_id = "rule_f0551fd5"
    score_deduction = 0  # 规则扣分

    try:
        # 检查文档类型是否为首次病程记录
        if medical_record.get("document_type") != "Initial Progress Note":
            return {
                "passed": True,
                "score_deduction": 0,
                "message": "非首次病程记录，无需检查签名规则",
                "rule_id": rule_id
            }

        signatures = medical_record.get("signatures", [])
        if not signatures:
            return {
                "passed": False,
                "score_deduction": score_deduction,
                "message": "首次病程记录缺少医师签名",
                "rule_id": rule_id
            }

        # 检查是否有实习医务人员签名
        intern_signatures = [s for s in signatures if s.get("signer_type") == "intern"]
        if intern_signatures:
            # 实习医务人员书写时，检查是否有经治医师或带教老师双签名
            attending_signatures = [s for s in signatures if s.get("signer_type") in ["attending", "supervisor"]]
            if len(attending_signatures) < 1:
                return {
                    "passed": False,
                    "score_deduction": score_deduction,
                    "message": "实习医务人员书写的首次病程记录缺少经治医师/带教老师签名",
                    "rule_id": rule_id
                }

        # 检查是否有医师签名（非实习）
        physician_signatures = [s for s in signatures if
                                s.get("signer_type") in ["attending", "resident", "supervisor"]]
        if not physician_signatures:
            return {
                "passed": False,
                "score_deduction": score_deduction,
                "message": "首次病程记录缺少医师签名",
                "rule_id": rule_id
            }

        return {
            "passed": True,
            "score_deduction": 0,
            "message": "首次病程记录签名完整",
            "rule_id": rule_id
        }

    except Exception as e:
        return {
            "passed": False,
            "score_deduction": score_deduction,
            "message": f"检查过程中发生错误: {str(e)}",
            "rule_id": rule_id
        }


def rule_8492b677(medical_record: dict) -> dict:
    """
    检查首次病程记录是否在患者入院后8小时内完成。

    参数:
        medical_record (dict): 医疗记录字典，应包含以下字段：
            - 'document_type': str, 文档类型
            - 'admission_time': str, 患者入院时间，格式为 'YYYY-MM-DD HH:MM:SS'
            - 'document_time': str, 首次病程记录完成时间，格式为 'YYYY-MM-DD HH:MM:SS'

    返回:
        dict: 检查结果，包含：
            - 'passed': bool, 是否通过检查
            - 'score_deduction': int, 扣分（未通过为60，通过为0）
            - 'message': str, 检查结果说明
            - 'rule_id': str, 规则ID
    """
    rule_id = "rule_8492b677"

    try:
        # 检查是否为首次病程记录
        if medical_record.get('document_type') != 'Initial Progress Note':
            return {
                "passed": True,
                "score_deduction": 0,
                "message": "非首次病程记录，无需检查",
                "rule_id": rule_id
            }

        # 获取入院时间和记录时间
        admission_time_str = medical_record.get('admission_time')
        document_time_str = medical_record.get('document_time')

        if not admission_time_str or not document_time_str:
            return {
                "passed": False,
                "score_deduction": 60,
                "message": "缺少入院时间或首次病程记录时间",
                "rule_id": rule_id
            }

        # 解析时间
        admission_time = datetime.strptime(admission_time_str, '%Y-%m-%d %H:%M:%S')
        document_time = datetime.strptime(document_time_str, '%Y-%m-%d %H:%M:%S')

        # 计算时间差（小时）
        time_diff_hours = (document_time - admission_time).total_seconds() / 3600

        # 判断是否超过8小时
        if time_diff_hours > 8:
            return {
                "passed": False,
                "score_deduction": 60,
                "message": f"首次病程记录未在入院后8小时内完成（实际耗时{time_diff_hours:.1f}小时）",
                "rule_id": rule_id
            }
        else:
            return {
                "passed": True,
                "score_deduction": 0,
                "message": "首次病程记录已在入院后8小时内完成",
                "rule_id": rule_id
            }

    except ValueError as e:
        return {
            "passed": False,
            "score_deduction": 60,
            "message": f"时间格式错误: {str(e)}",
            "rule_id": rule_id
        }
    except Exception as e:
        return {
            "passed": False,
            "score_deduction": 60,
            "message": f"检查过程中发生错误: {str(e)}",
            "rule_id": rule_id
        }


def check_all_rules(medical_record: Dict[str, Any]) -> Dict[str, Any]:
    """
    检查医疗记录是否符合所有Initial Progress Note质控规则

    Args:
        medical_record: 医疗记录字典

    Returns:
        检查结果字典，包含每个规则的检查结果
    """
    results = {}
    total_score_deduction = 0

    logger.info(f"开始检查Initial Progress Note质控规则，共6个规则")

    # 调用所有规则检查函数
    try:
        result = rule_5e6b4944(medical_record)
        results[result["rule_id"]] = result
        total_score_deduction += result["score_deduction"]
    except Exception as e:
        logger.error(f"规则 rule_5e6b4944 检查时出错: {e}")
        results["rule_5e6b4944"] = {
            "passed": False,
            "score_deduction": 6,
            "message": f"检查出错: {e}",
            "rule_id": "rule_5e6b4944"
        }
    try:
        result = rule_348799f5(medical_record)
        results[result["rule_id"]] = result
        total_score_deduction += result["score_deduction"]
    except Exception as e:
        logger.error(f"规则 rule_348799f5 检查时出错: {e}")
        results["rule_348799f5"] = {
            "passed": False,
            "score_deduction": 6,
            "message": f"检查出错: {e}",
            "rule_id": "rule_348799f5"
        }
    try:
        result = rule_d40e19cb(medical_record)
        results[result["rule_id"]] = result
        total_score_deduction += result["score_deduction"]
    except Exception as e:
        logger.error(f"规则 rule_d40e19cb 检查时出错: {e}")
        results["rule_d40e19cb"] = {
            "passed": False,
            "score_deduction": 6,
            "message": f"检查出错: {e}",
            "rule_id": "rule_d40e19cb"
        }
    try:
        result = rule_0462ecb1(medical_record)
        results[result["rule_id"]] = result
        total_score_deduction += result["score_deduction"]
    except Exception as e:
        logger.error(f"规则 rule_0462ecb1 检查时出错: {e}")
        results["rule_0462ecb1"] = {
            "passed": False,
            "score_deduction": 6,
            "message": f"检查出错: {e}",
            "rule_id": "rule_0462ecb1"
        }
    try:
        result = rule_f0551fd5(medical_record)
        results[result["rule_id"]] = result
        total_score_deduction += result["score_deduction"]
    except Exception as e:
        logger.error(f"规则 rule_f0551fd5 检查时出错: {e}")
        results["rule_f0551fd5"] = {
            "passed": False,
            "score_deduction": 0,
            "message": f"检查出错: {e}",
            "rule_id": "rule_f0551fd5"
        }
    try:
        result = rule_8492b677(medical_record)
        results[result["rule_id"]] = result
        total_score_deduction += result["score_deduction"]
    except Exception as e:
        logger.error(f"规则 rule_8492b677 检查时出错: {e}")
        results["rule_8492b677"] = {
            "passed": False,
            "score_deduction": 60,
            "message": f"检查出错: {e}",
            "rule_id": "rule_8492b677"
        }

    # 汇总结果
    passed_count = sum(1 for result in results.values() if result["passed"])
    failed_count = len(results) - passed_count

    summary = {
        "total_rules": len(results),
        "passed_count": passed_count,
        "failed_count": failed_count,
        "total_score_deduction": total_score_deduction,
        "individual_results": results
    }

    logger.info(
        f"Initial Progress Note质控检查完成，通过: {passed_count}, 失败: {failed_count}, 总扣分: {total_score_deduction}")

    return summary