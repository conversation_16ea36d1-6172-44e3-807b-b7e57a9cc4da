# 规则质控模块 (Regulatory Quality Control)

## 模块概述

规则质控模块负责自动生成和执行基于代码的医疗病历质控规则。该模块将质控标准转换为可执行的Python函数，实现结构化、自动化的病历质量检查。

## 目录结构

```
dev_v1/Regulatory_Quality_Control/
├── rule_code_generator.py                           # 规则代码生成器
├── regulatory_processor.py                          # 规则处理器
├── Initial_Progress_Note_regulatory_quality_control.py  # 首次病程记录质控规则
├── __pycache__/                                     # Python缓存文件
└── README.md                                        # 本说明文档
```

## 核心功能

### 1. 规则代码生成器 (rule_code_generator.py)
- **自动代码生成**：将质控规则转换为Python函数
- **函数模板管理**：提供标准化的质控函数模板
- **代码验证**：确保生成的代码语法正确且可执行
- **文件管理**：按病历类型组织生成的质控函数

### 2. 规则处理器 (regulatory_processor.py)
- **规则解析**：解析质控规则的逻辑结构
- **条件判断**：处理复杂的条件性质控规则
- **评分计算**：实现质控评分和扣分逻辑
- **结果输出**：标准化质控结果格式

### 3. 质控规则文件
- **按文档类型分类**：每种病历文档对应一个质控文件
- **函数化实现**：每个质控标准对应一个独立函数
- **标准化接口**：统一的函数签名和返回格式

## 技术特点

### 代码生成规范
```python
def rule_name_check(patient_info, medical_record):
    """
    质控规则检查函数
    Args:
        patient_info: 病人基本信息字典
        medical_record: 病历内容字典
    Returns:
        dict: {
            "score": int,           # 得分
            "deduction": list,      # 扣分项列表
            "message": str,         # 检查结果说明
            "status": str          # 检查状态 (pass/warning/error)
        }
    """
    # 质控逻辑实现
    pass
```

### 支持的质控类型
1. **结构完整性检查**
   - 必填字段验证
   - 章节完整性检查
   - 格式规范验证

2. **时效性检查**
   - 文档完成时间验证
   - 时间逻辑一致性检查

3. **数据一致性检查**
   - 跨字段数据一致性
   - 逻辑关系验证

## 使用方法

### 基本使用
```python
from regulatory_processor import RegulatoryProcessor

# 创建处理器实例
processor = RegulatoryProcessor()

# 加载质控规则
processor.load_rules("Initial_Progress_Note")

# 执行质控检查
result = processor.check_quality(patient_info, medical_record)
```

### 代码生成
```python
from rule_code_generator import RuleCodeGenerator

# 创建代码生成器
generator = RuleCodeGenerator()

# 生成质控函数
generator.generate_rule_function(rule_data, output_file)
```

## 输出格式

### 质控结果格式
```json
{
    "overall_score": 85,
    "rule_results": [
        {
            "rule_name": "必填字段检查",
            "score": 10,
            "deduction": 0,
            "status": "pass",
            "message": "所有必填字段完整"
        }
    ],
    "total_deduction": 15,
    "status": "warning"
}
```

## 扩展开发

### 添加新的质控规则
1. 在相应的质控文件中添加新函数
2. 遵循标准函数签名和返回格式
3. 添加适当的文档字符串和注释
4. 进行单元测试验证

### 支持新的文档类型
1. 创建新的质控文件
2. 实现该文档类型的所有质控规则
3. 在处理器中注册新的文档类型
4. 更新配置和文档

## 依赖要求

- Python 3.7+
- 标准库模块：json, datetime, re
- 项目依赖：config, model_use

## 注意事项

1. **代码安全**：生成的代码需要进行安全检查
2. **性能优化**：避免在质控函数中进行耗时操作
3. **错误处理**：确保质控函数有完善的异常处理
4. **版本管理**：质控规则的版本控制和向后兼容

---

本模块是医疗病历质控智能代理系统的核心组件之一，负责将专家经验转化为可执行的质控代码，实现自动化、标准化的病历质量管理。
