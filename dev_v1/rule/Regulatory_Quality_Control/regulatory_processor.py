# -*- coding: utf-8 -*-
import os
import json
import logging
from collections import defaultdict
from datetime import datetime
from rule_code_generator import RuleCodeGenerator

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RegulatoryProcessor:
    """规则质控处理器"""
    
    def __init__(self, output_dir=None):
        self.output_dir = output_dir or os.path.dirname(os.path.abspath(__file__))
        self.code_generator = RuleCodeGenerator()
        logger.info(f"规则质控处理器初始化完成，输出目录: {self.output_dir}")
    
    def load_rules_from_json(self, json_file_path):
        """从JSON文件加载规则数据"""
        try:
            logger.info(f"正在加载规则文件: {json_file_path}")
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            rules = data.get('rules', [])
            logger.info(f"成功加载 {len(rules)} 个规则")
            return rules
            
        except Exception as e:
            logger.error(f"加载规则文件时出错: {e}")
            return []
    
    def classify_rules_by_type(self, rules):
        """按类型分类规则"""
        logger.info("开始按类型分类规则")
        
        classified_rules = {
            '规则': [],
            '内涵': [],
            '其他': []
        }
        
        for rule in rules:
            rule_type = rule.get('type', '其他')
            if rule_type in classified_rules:
                classified_rules[rule_type].append(rule)
            else:
                classified_rules['其他'].append(rule)
        
        # 记录分类结果
        for rule_type, rule_list in classified_rules.items():
            logger.info(f"类型 '{rule_type}': {len(rule_list)} 个规则")
        
        return classified_rules
    
    def group_rules_by_document_type(self, rules):
        """按文档类型分组规则"""
        logger.info("开始按文档类型分组规则")
        
        grouped_rules = defaultdict(list)
        
        for rule in rules:
            document_type = rule.get('document_type', 'Unknown')
            grouped_rules[document_type].append(rule)
        
        # 记录分组结果
        for doc_type, rule_list in grouped_rules.items():
            logger.info(f"文档类型 '{doc_type}': {len(rule_list)} 个规则")
        
        return dict(grouped_rules)
    
    def process_regulatory_rules(self, json_file_path):
        """处理规则质控规则，生成对应的Python代码文件"""
        logger.info(f"开始处理规则质控文件: {json_file_path}")
        
        # 加载规则
        all_rules = self.load_rules_from_json(json_file_path)
        if not all_rules:
            logger.error("没有加载到任何规则")
            return
        
        # 按类型分类规则
        classified_rules = self.classify_rules_by_type(all_rules)
        
        # 只处理"规则"类型的质控标准
        regulatory_rules = classified_rules.get('规则', [])
        
        if not regulatory_rules:
            logger.warning("没有找到'规则'类型的质控标准")
            return
        
        logger.info(f"找到 {len(regulatory_rules)} 个'规则'类型的质控标准")
        
        # 按文档类型分组
        grouped_rules = self.group_rules_by_document_type(regulatory_rules)
        
        # 为每个文档类型生成代码文件
        generated_files = []
        for document_type, rules in grouped_rules.items():
            logger.info(f"正在为文档类型 '{document_type}' 生成代码文件")
            
            try:
                filepath = self.code_generator.save_generated_code(
                    rules, document_type, self.output_dir
                )
                
                if filepath:
                    generated_files.append(filepath)
                    logger.info(f"成功生成代码文件: {filepath}")
                else:
                    logger.error(f"生成文档类型 '{document_type}' 的代码文件失败")
                    
            except Exception as e:
                logger.error(f"处理文档类型 '{document_type}' 时出错: {e}")
        
        # 生成处理报告
        self.generate_processing_report(json_file_path, classified_rules, generated_files)
        
        logger.info(f"规则质控处理完成，共生成 {len(generated_files)} 个代码文件")
        return generated_files
    
    def generate_processing_report(self, source_file, classified_rules, generated_files):
        """生成处理报告"""
        try:
            report = {
                "processing_time": datetime.now().isoformat(),
                "source_file": source_file,
                "classification_summary": {
                    rule_type: len(rules) for rule_type, rules in classified_rules.items()
                },
                "generated_files": generated_files,
                "total_generated_files": len(generated_files)
            }
            
            report_file = os.path.join(self.output_dir, "processing_report.json")
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"处理报告已保存到: {report_file}")
            
        except Exception as e:
            logger.error(f"生成处理报告时出错: {e}")
    
    def process_initial_progress_note(self):
        """处理Initial Progress Note规则文件"""
        json_file_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'rule',
            'Initial Progress Note_type.json'
        )
        
        logger.info(f"开始处理Initial Progress Note规则文件: {json_file_path}")
        
        if not os.path.exists(json_file_path):
            logger.error(f"规则文件不存在: {json_file_path}")
            return
        
        return self.process_regulatory_rules(json_file_path)

def main():
    """主函数"""
    logger.info("开始执行规则质控处理程序")
    
    # 创建处理器
    processor = RegulatoryProcessor()
    
    # 处理Initial Progress Note规则文件
    generated_files = processor.process_initial_progress_note()
    
    if generated_files:
        print(f"\n处理完成！生成了以下代码文件:")
        for file_path in generated_files:
            print(f"  - {file_path}")
    else:
        print("\n处理完成，但没有生成任何代码文件。")
    
    logger.info("规则质控处理程序执行完成")

if __name__ == '__main__':
    main()